/* globals STANDALONE */

import React, { useEffect, useRef, useCallback } from 'react';
import ReactDOM from 'react-dom';

const cancelMouseEvent = e => {
  e.stopPropagation();
};

/**
 * Functional implementation of AppcuesBuilder Web Component
 * 
 * This is a functional version of the class-based AppcuesBuilder component,
 * using React hooks to manage the component lifecycle and state.
 */
const AppcuesBuilderFunctional = () => {
  const shadowRootRef = useRef(null);
  const containerRef = useRef(null);
  const observerRef = useRef(null);

  // Observed attributes that trigger re-renders
  const observedAttributes = [
    'auth-token',
    'experienceId',
    'theme',
    'builder-pattern',
  ];

  /**
   * Initialize Shadow DOM and create the basic structure
   */
  const initializeShadowDOM = useCallback(() => {
    if (!containerRef.current || shadowRootRef.current) return;

    const shadowRoot = containerRef.current.attachShadow({ mode: 'open' });
    shadowRootRef.current = shadowRoot;

    // Set default theme
    const defaultTheme = containerRef.current.getAttribute('theme') || 'dark';

    shadowRoot.innerHTML = `
      <div id="react-root" data-theme="${defaultTheme}" dir="ltr"></div>
      <div id="portal-root" dir="ltr">
      </div>
      <div data-theme="light" id="portal-root-light" dir="ltr"
      style="position: relative;isolation: isolate;z-index: ${Number.MAX_SAFE_INTEGER}">
      </div>
    `;

    // Add createElement methods to shadowRoot for React compatibility
    shadowRoot.createElement = (...args) => document.createElement(...args);
    shadowRoot.createElementNS = (...args) => document.createElementNS(...args);
    shadowRoot.createTextNode = (...args) => document.createTextNode(...args);

    return shadowRoot;
  }, []);

  /**
   * Set up event listeners and React root
   */
  const setupEventListeners = useCallback(() => {
    if (!shadowRootRef.current) return;

    const shadowRoot = shadowRootRef.current;
    const $reactRoot = shadowRoot.querySelector('#react-root');
    const $portalRoot = shadowRoot.querySelector('#portal-root');

    if (!$reactRoot || !$portalRoot) return;

    // Set ownerDocument property for React compatibility
    Object.defineProperty($reactRoot, 'ownerDocument', {
      value: shadowRoot,
    });

    // Dispatch builder:connected event
    const event = new CustomEvent('builder:connected', {
      detail: {
        rootContainer: $reactRoot,
        container: containerRef.current,
        shadowRoot: shadowRoot,
      },
    });

    // Dispatch event based on STANDALONE mode
    (STANDALONE ? window : containerRef.current).dispatchEvent(event);

    // Add event listeners to prevent event bubbling
    $portalRoot.addEventListener('click', cancelMouseEvent);
    $portalRoot.addEventListener('pointerdown', cancelMouseEvent);
    $reactRoot.addEventListener('click', cancelMouseEvent);
    $reactRoot.addEventListener('pointerdown', cancelMouseEvent);

    // Return cleanup function
    return () => {
      $portalRoot.removeEventListener('click', cancelMouseEvent);
      $portalRoot.removeEventListener('pointerdown', cancelMouseEvent);
      $reactRoot.removeEventListener('click', cancelMouseEvent);
      $reactRoot.removeEventListener('pointerdown', cancelMouseEvent);
    };
  }, []);

  /**
   * Set theme on the react root element
   */
  const setTheme = useCallback((newTheme) => {
    if (!shadowRootRef.current) return;

    const $reactRoot = shadowRootRef.current.querySelector('#react-root');
    if ($reactRoot) {
      $reactRoot.dataset.theme = newTheme;
    }
  }, []);

  /**
   * Emit change event when attributes change
   */
  const emitChangeEvent = useCallback((name, newValue, oldValue = null) => {
    if (!containerRef.current) return;

    const event = new CustomEvent('builder:change', {
      detail: {
        name,
        newValue,
        oldValue,
      },
    });
    containerRef.current.dispatchEvent(event);
  }, []);

  /**
   * Handle attribute changes
   */
  const handleAttributeChange = useCallback((name, oldValue, newValue) => {
    if (name === 'theme') {
      setTheme(newValue);
    }

    emitChangeEvent(name, newValue, oldValue);
  }, [setTheme, emitChangeEvent]);

  /**
   * Set up mutation observer to watch for attribute changes
   */
  const setupMutationObserver = useCallback(() => {
    if (!containerRef.current || observerRef.current) return;

    const observer = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'attributes') {
          const name = mutation.attributeName;
          const oldValue = mutation.oldValue;
          const newValue = containerRef.current.getAttribute(name);

          if (observedAttributes.includes(name)) {
            handleAttributeChange(name, oldValue, newValue);
          }
        }
      }
    });

    observer.observe(containerRef.current, {
      attributes: true,
      attributeOldValue: true,
      attributeFilter: observedAttributes,
    });

    observerRef.current = observer;

    return () => {
      observer.disconnect();
      observerRef.current = null;
    };
  }, [handleAttributeChange, observedAttributes]);

  /**
   * Cleanup function for component unmount
   */
  const cleanup = useCallback(() => {
    if (shadowRootRef.current) {
      const $reactRoot = shadowRootRef.current.querySelector('#react-root');
      if ($reactRoot) {
        ReactDOM.unmountComponentAtNode($reactRoot);
      }
    }

    if (observerRef.current) {
      observerRef.current.disconnect();
      observerRef.current = null;
    }
  }, []);

  /**
   * Initialize component when container ref is set
   */
  useEffect(() => {
    if (!containerRef.current) return;

    const shadowRoot = initializeShadowDOM();
    if (!shadowRoot) return;

    const cleanupEvents = setupEventListeners();
    const cleanupObserver = setupMutationObserver();

    // Return cleanup function
    return () => {
      cleanup();
      cleanupEvents?.();
      cleanupObserver?.();
    };
  }, [initializeShadowDOM, setupEventListeners, setupMutationObserver, cleanup]);

  // This component doesn't render anything directly
  // It manages the Web Component lifecycle through refs and effects
  return <div ref={containerRef} style={{ display: 'none' }} />;
};

/**
 * Factory function to create and register the custom element
 */
export const createAppcuesBuilderElement = () => {
  class AppcuesBuilderElement extends HTMLElement {
    constructor() {
      super();
      this.reactComponent = null;
    }

    connectedCallback() {
      // Create a React component instance and render it
      this.reactComponent = React.createElement(AppcuesBuilderFunctional);
      
      // We need to render this somewhere, but the actual functionality
      // is handled through the ref in the functional component
      const wrapper = document.createElement('div');
      this.appendChild(wrapper);
      ReactDOM.render(this.reactComponent, wrapper);
    }

    disconnectedCallback() {
      if (this.firstChild) {
        ReactDOM.unmountComponentAtNode(this.firstChild);
      }
    }

    static get observedAttributes() {
      return [
        'auth-token',
        'experienceId',
        'theme',
        'builder-pattern',
      ];
    }

    attributeChangedCallback(name, oldValue, newValue) {
      // The mutation observer in the functional component will handle this
    }
  }

  return AppcuesBuilderElement;
};

export default AppcuesBuilderFunctional;
