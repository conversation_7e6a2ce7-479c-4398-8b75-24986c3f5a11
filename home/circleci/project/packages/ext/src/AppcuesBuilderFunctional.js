/* globals STANDALONE */

import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom/client';

const cancelMouseEvent = e => {
  e.stopPropagation();
};

const AppcuesBuilderFunctional = () => {
  const [theme, setTheme] = useState('dark');

  useEffect(() => {
    // 获取或创建 Shadow DOM
    const hostElement = document.currentScript?.parentNode;
    if (!hostElement) return;

    let shadowRoot = hostElement.shadowRoot;
    if (!shadowRoot) {
      shadowRoot = hostElement.attachShadow({ mode: 'open' });
      shadowRoot.innerHTML = `
        <div id="react-root" data-theme=${theme} dir="ltr"></div>
        <div id="portal-root" dir="ltr"></div>
        <div data-theme="light" id="portal-root-light" dir="ltr"
          style="position: relative;isolation: isolate;z-index: ${Number.MAX_SAFE_INTEGER}"></div>
      `;
      // 添加类似 document 的方法
      shadowRoot.createElement = (...args) => document.createElement(...args);
      shadowRoot.createElementNS = (...args) => document.createElementNS(...args);
      shadowRoot.createTextNode = (...args) => document.createTextNode(...args);
    }

    const $reactRoot = shadowRoot.querySelector('#react-root');
    const $portalRoot = shadowRoot.querySelector('#portal-root');

    Object.defineProperty($reactRoot, 'ownerDocument', {
      value: shadowRoot,
    });

    const event = new CustomEvent('builder:connected', {
      detail: {
        rootContainer: $reactRoot,
        container: hostElement,
        shadowRoot: shadowRoot,
      },
    });

    (STANDALONE ? window : hostElement).dispatchEvent(event);

    $portalRoot.addEventListener('click', cancelMouseEvent);
    $portalRoot.addEventListener('pointerdown', cancelMouseEvent);

    $reactRoot.addEventListener('click', cancelMouseEvent);
    $reactRoot.addEventListener('pointerdown', cancelMouseEvent);

    // 渲染 React 应用到 #react-root
    const root = ReactDOM.createRoot($reactRoot);
    root.render(<div>Your React content here</div>);

    return () => {
      root.unmount();
      $portalRoot.removeEventListener('click', cancelMouseEvent);
      $portalRoot.removeEventListener('pointerdown', cancelMouseEvent);
      $reactRoot.removeEventListener('click', cancelMouseEvent);
      $reactRoot.removeEventListener('pointerdown', cancelMouseEvent);
    };
  }, [theme]);

  useEffect(() => {
    const hostElement = document.currentScript?.parentNode;
    if (!hostElement) return;
    const shadowRoot = hostElement.shadowRoot;
    if (shadowRoot) {
      const $reactRoot = shadowRoot.querySelector('#react-root');
      $reactRoot.dataset.theme = theme;
    }
  }, [theme]);

  const emitChangeEvent = (name, newValue, oldValue = null) => {
    const hostElement = document.currentScript?.parentNode;
    if (!hostElement) return;
    const event = new CustomEvent('builder:change', {
      detail: {
        name,
        newValue,
        oldValue,
      },
    });
    hostElement.dispatchEvent(event);
  };

  useEffect(() => {
    const hostElement = document.currentScript?.parentNode;
    if (!hostElement) return;

    const observer = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'attributes') {
          const name = mutation.attributeName;
          const oldValue = mutation.oldValue;
          const newValue = hostElement.getAttribute(name);

          if (name === 'theme') {
            setTheme(newValue);
          }

          emitChangeEvent(name, newValue, oldValue);
        }
      }
    });

    observer.observe(hostElement, { attributes: true, attributeOldValue: true });

    return () => {
      observer.disconnect();
    };
  }, []);

  // 不返回任何 React 元素
  return null;
};

export default AppcuesBuilderFunctional;
