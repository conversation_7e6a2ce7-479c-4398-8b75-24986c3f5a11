/* globals STANDALONE */

import { useEffect, useRef, useCallback, useState } from 'react';
import ReactDOM from 'react-dom';

const cancelMouseEvent = e => {
  e.stopPropagation();
};

/**
 * Custom hook for managing AppcuesBuilder Web Component functionality
 * 
 * This hook encapsulates all the logic from the original class-based component
 * using modern React patterns and hooks.
 */
export const useAppcuesBuilder = (hostElement) => {
  const [theme, setTheme] = useState('dark');
  const shadowRootRef = useRef(null);
  const cleanupFunctionsRef = useRef([]);

  const observedAttributes = [
    'auth-token',
    'experienceId',
    'theme',
    'builder-pattern',
  ];

  /**
   * Initialize Shadow DOM structure
   */
  const initializeShadowDOM = useCallback(() => {
    if (!hostElement || shadowRootRef.current) return null;

    const shadowRoot = hostElement.attachShadow({ mode: 'open' });
    shadowRootRef.current = shadowRoot;

    // Get initial theme from attribute or use default
    const initialTheme = hostElement.getAttribute('theme') || 'dark';
    setTheme(initialTheme);

    shadowRoot.innerHTML = `
      <div id="react-root" data-theme="${initialTheme}" dir="ltr"></div>
      <div id="portal-root" dir="ltr"></div>
      <div data-theme="light" id="portal-root-light" dir="ltr"
           style="position: relative; isolation: isolate; z-index: ${Number.MAX_SAFE_INTEGER}">
      </div>
    `;

    // Add createElement methods for React compatibility
    shadowRoot.createElement = (...args) => document.createElement(...args);
    shadowRoot.createElementNS = (...args) => document.createElementNS(...args);
    shadowRoot.createTextNode = (...args) => document.createTextNode(...args);

    return shadowRoot;
  }, [hostElement]);

  /**
   * Set up event listeners and dispatch connected event
   */
  const setupEventListeners = useCallback(() => {
    if (!shadowRootRef.current || !hostElement) return;

    const shadowRoot = shadowRootRef.current;
    const $reactRoot = shadowRoot.querySelector('#react-root');
    const $portalRoot = shadowRoot.querySelector('#portal-root');

    if (!$reactRoot || !$portalRoot) return;

    // Set ownerDocument for React compatibility
    Object.defineProperty($reactRoot, 'ownerDocument', {
      value: shadowRoot,
    });

    // Dispatch builder:connected event
    const connectedEvent = new CustomEvent('builder:connected', {
      detail: {
        rootContainer: $reactRoot,
        container: hostElement,
        shadowRoot: shadowRoot,
      },
    });

    (STANDALONE ? window : hostElement).dispatchEvent(connectedEvent);

    // Add event listeners to prevent bubbling
    const eventListeners = [
      [$portalRoot, 'click', cancelMouseEvent],
      [$portalRoot, 'pointerdown', cancelMouseEvent],
      [$reactRoot, 'click', cancelMouseEvent],
      [$reactRoot, 'pointerdown', cancelMouseEvent],
    ];

    eventListeners.forEach(([element, event, handler]) => {
      element.addEventListener(event, handler);
    });

    // Store cleanup function
    const cleanup = () => {
      eventListeners.forEach(([element, event, handler]) => {
        element.removeEventListener(event, handler);
      });
    };

    cleanupFunctionsRef.current.push(cleanup);
  }, [hostElement]);

  /**
   * Update theme in the shadow DOM
   */
  const updateTheme = useCallback((newTheme) => {
    if (!shadowRootRef.current) return;

    const $reactRoot = shadowRootRef.current.querySelector('#react-root');
    if ($reactRoot) {
      $reactRoot.dataset.theme = newTheme;
    }
    setTheme(newTheme);
  }, []);

  /**
   * Emit change event for attribute changes
   */
  const emitChangeEvent = useCallback((name, newValue, oldValue = null) => {
    if (!hostElement) return;

    const changeEvent = new CustomEvent('builder:change', {
      detail: { name, newValue, oldValue },
    });
    hostElement.dispatchEvent(changeEvent);
  }, [hostElement]);

  /**
   * Handle attribute changes
   */
  const handleAttributeChange = useCallback((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes') {
        const { attributeName: name, oldValue } = mutation;
        const newValue = hostElement.getAttribute(name);

        if (observedAttributes.includes(name)) {
          if (name === 'theme') {
            updateTheme(newValue);
          }
          emitChangeEvent(name, newValue, oldValue);
        }
      }
    });
  }, [hostElement, updateTheme, emitChangeEvent, observedAttributes]);

  /**
   * Set up mutation observer for attribute changes
   */
  const setupMutationObserver = useCallback(() => {
    if (!hostElement) return;

    const observer = new MutationObserver(handleAttributeChange);
    observer.observe(hostElement, {
      attributes: true,
      attributeOldValue: true,
      attributeFilter: observedAttributes,
    });

    const cleanup = () => observer.disconnect();
    cleanupFunctionsRef.current.push(cleanup);
  }, [hostElement, handleAttributeChange, observedAttributes]);

  /**
   * Cleanup function for unmounting
   */
  const cleanup = useCallback(() => {
    // Unmount React components
    if (shadowRootRef.current) {
      const $reactRoot = shadowRootRef.current.querySelector('#react-root');
      if ($reactRoot) {
        ReactDOM.unmountComponentAtNode($reactRoot);
      }
    }

    // Run all cleanup functions
    cleanupFunctionsRef.current.forEach(fn => fn());
    cleanupFunctionsRef.current = [];
    shadowRootRef.current = null;
  }, []);

  /**
   * Initialize the component
   */
  useEffect(() => {
    if (!hostElement) return;

    const shadowRoot = initializeShadowDOM();
    if (shadowRoot) {
      setupEventListeners();
      setupMutationObserver();
    }

    return cleanup;
  }, [hostElement, initializeShadowDOM, setupEventListeners, setupMutationObserver, cleanup]);

  return {
    shadowRoot: shadowRootRef.current,
    theme,
    setTheme: updateTheme,
    emitChangeEvent,
    cleanup,
  };
};

/**
 * Functional Web Component factory
 * 
 * Creates a Web Component class that uses the useAppcuesBuilder hook
 */
export const createFunctionalAppcuesBuilder = () => {
  return class FunctionalAppcuesBuilder extends HTMLElement {
    static observedAttributes = [
      'auth-token',
      'experienceId',
      'theme',
      'builder-pattern',
    ];

    constructor() {
      super();
      this.builderHook = null;
    }

    connectedCallback() {
      // Initialize the hook-based functionality
      // Note: In a real implementation, you'd need a way to run hooks
      // This is a conceptual example showing how the logic would be organized
      this.builderHook = {
        shadowRoot: null,
        theme: 'dark',
        cleanup: () => {},
      };

      // Initialize shadow DOM manually (since we can't use hooks directly in a class)
      this.initializeManually();
    }

    disconnectedCallback() {
      if (this.builderHook?.cleanup) {
        this.builderHook.cleanup();
      }
    }

    attributeChangedCallback(name, oldValue, newValue) {
      if (name === 'theme' && this.builderHook) {
        this.setTheme(newValue);
      }
      this.emitChangeEvent(name, newValue, oldValue);
    }

    initializeManually() {
      // Manual implementation of the hook logic for Web Component
      const shadowRoot = this.attachShadow({ mode: 'open' });
      const theme = this.getAttribute('theme') || 'dark';

      shadowRoot.innerHTML = `
        <div id="react-root" data-theme="${theme}" dir="ltr"></div>
        <div id="portal-root" dir="ltr"></div>
        <div data-theme="light" id="portal-root-light" dir="ltr"
             style="position: relative; isolation: isolate; z-index: ${Number.MAX_SAFE_INTEGER}">
        </div>
      `;

      shadowRoot.createElement = (...args) => document.createElement(...args);
      shadowRoot.createElementNS = (...args) => document.createElementNS(...args);
      shadowRoot.createTextNode = (...args) => document.createTextNode(...args);

      this.setupEventListeners(shadowRoot);
      this.builderHook.shadowRoot = shadowRoot;
    }

    setupEventListeners(shadowRoot) {
      const $reactRoot = shadowRoot.querySelector('#react-root');
      const $portalRoot = shadowRoot.querySelector('#portal-root');

      Object.defineProperty($reactRoot, 'ownerDocument', { value: shadowRoot });

      const event = new CustomEvent('builder:connected', {
        detail: {
          rootContainer: $reactRoot,
          container: this,
          shadowRoot: shadowRoot,
        },
      });

      (STANDALONE ? window : this).dispatchEvent(event);

      [$portalRoot, $reactRoot].forEach(element => {
        element.addEventListener('click', cancelMouseEvent);
        element.addEventListener('pointerdown', cancelMouseEvent);
      });
    }

    setTheme(newTheme) {
      if (this.builderHook?.shadowRoot) {
        const $reactRoot = this.builderHook.shadowRoot.querySelector('#react-root');
        if ($reactRoot) {
          $reactRoot.dataset.theme = newTheme;
        }
      }
    }

    emitChangeEvent(name, newValue, oldValue = null) {
      const event = new CustomEvent('builder:change', {
        detail: { name, newValue, oldValue },
      });
      this.dispatchEvent(event);
    }
  };
};

export default useAppcuesBuilder;
